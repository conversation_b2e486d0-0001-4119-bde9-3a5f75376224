import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';
import 'package:selfeng/services/crashlytics_service/utils/error_handler.dart';
import 'package:selfeng/services/in_app_update_service/domain/providers/in_app_update_service_provider.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/in_app_update_service_repository.dart';

part 'in_app_update_controller.g.dart';

/// State for in-app update
class InAppUpdateState {
  final bool isLoading;
  final bool isUpdateAvailable;
  final bool isUpdateDownloaded;
  final bool isHighPriority;
  final AppUpdateInfo? updateInfo;
  final String? error;

  const InAppUpdateState({
    this.isLoading = false,
    this.isUpdateAvailable = false,
    this.isUpdateDownloaded = false,
    this.isHighPriority = false,
    this.updateInfo,
    this.error,
  });

  InAppUpdateState copyWith({
    bool? isLoading,
    bool? isUpdateAvailable,
    bool? isUpdateDownloaded,
    bool? isHighPriority,
    AppUpdateInfo? updateInfo,
    String? error,
  }) {
    return InAppUpdateState(
      isLoading: isLoading ?? this.isLoading,
      isUpdateAvailable: isUpdateAvailable ?? this.isUpdateAvailable,
      isUpdateDownloaded: isUpdateDownloaded ?? this.isUpdateDownloaded,
      isHighPriority: isHighPriority ?? this.isHighPriority,
      updateInfo: updateInfo ?? this.updateInfo,
      error: error ?? this.error,
    );
  }
}

/// Controller for managing in-app updates using new Riverpod 3.0 API
@riverpod
class InAppUpdateController extends _$InAppUpdateController {
  @override
  InAppUpdateState build() {
    // Initialize crashlytics context asynchronously to avoid setState during build
    _initializeCrashlyticsAsync();
    return const InAppUpdateState();
  }

  InAppUpdateServiceRepository get _updateService =>
      ref.watch(inAppUpdateServiceProvider);
  ErrorHandler get _errorHandler =>
      ErrorHandler(ref.watch(crashlyticsServiceProvider));

  /// Initialize crashlytics service asynchronously to avoid setState during build
  void _initializeCrashlyticsAsync() {
    // Use microtask to defer initialization until after build phase
    Future.microtask(() async {
      try {
        final crashlytics = ref.read(crashlyticsServiceProvider);

        // Set custom keys for in-app update context
        await crashlytics.setCustomKeys({
          'feature': 'in_app_update',
          'service': 'in_app_update_controller',
        });

        // Log initialization for debugging context
        crashlytics.log('InAppUpdateController initialized');
      } catch (e) {
        // Don't let crashlytics initialization errors break the app
        debugPrint(
          'Failed to initialize crashlytics in InAppUpdateController: $e',
        );
      }
    });
  }

  /// Initialize the update service and check for updates
  Future<void> initialize() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Initialize the service
      final initResult = await _updateService.initialize();
      await initResult.fold(
        (error) async {
          await _errorHandler.reportBusinessLogicError(
            error,
            StackTrace.current,
            feature: 'in_app_update',
            operation: 'initialize_service',
            businessContext: {'error_message': error.message},
          );
          state = state.copyWith(isLoading: false, error: error.message);
        },
        (_) async {
          // Check for updates
          await checkForUpdates();
        },
      );
    } catch (e, stackTrace) {
      await _errorHandler.reportBusinessLogicError(
        e,
        stackTrace,
        feature: 'in_app_update',
        operation: 'initialize_service',
        businessContext: {'error_message': e.toString()},
      );
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initialize update service: $e',
      );
    }
  }

  /// Check for available updates
  Future<void> checkForUpdates() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _updateService.checkForUpdate();

      await result.fold(
        (error) async {
          await _errorHandler.reportBusinessLogicError(
            error,
            StackTrace.current,
            feature: 'in_app_update',
            operation: 'check_for_updates',
            businessContext: {'error_message': error.message},
          );
          state = state.copyWith(isLoading: false, error: error.message);
        },
        (updateInfo) async {
          if (updateInfo == null) {
            state = state.copyWith(
              isLoading: false,
              isUpdateAvailable: false,
              isUpdateDownloaded: false,
              isHighPriority: false,
              updateInfo: null,
            );
            return;
          }
          final isUpdateAvailable =
              updateInfo.updateAvailability ==
              UpdateAvailability.updateAvailable;

          // Check if flexible update is downloaded
          bool isDownloaded = false;
          if (isUpdateAvailable) {
            final downloadResult = await _updateService
                .isFlexibleUpdateDownloaded(updateInfo);
            isDownloaded = downloadResult.fold(
              (_) => false,
              (downloaded) => downloaded,
            );
          }

          // Check if the update is high priority
          final isHighPriorityResult = await _updateService
              .isHighPriorityUpdate(updateInfo);
          final isHighPriority = isHighPriorityResult.fold(
            (_) => false,
            (isHigh) => isHigh,
          );

          state = state.copyWith(
            isLoading: false,
            isUpdateAvailable: isUpdateAvailable,
            isUpdateDownloaded: isDownloaded,
            isHighPriority: isHighPriority,
            updateInfo: updateInfo,
          );
        },
      );
    } catch (e, stackTrace) {
      await _errorHandler.reportBusinessLogicError(
        e,
        stackTrace,
        feature: 'in_app_update',
        operation: 'check_for_updates',
        businessContext: {'error_message': e.toString()},
      );
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to check for updates: $e',
      );
    }
  }

  /// Start an immediate update (for critical updates)
  Future<void> startImmediateUpdate() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      if (state.updateInfo == null) {
        await _errorHandler.reportBusinessLogicError(
          Exception('Update info not available'),
          StackTrace.current,
          feature: 'in_app_update',
          operation: 'start_immediate_update',
          businessContext: {'reason': 'update_info_null'},
        );
        state = state.copyWith(
          isLoading: false,
          error: 'Update info not available',
        );
        return;
      }
      final result = await _updateService.startImmediateUpdate(
        state.updateInfo!,
      );

      result.fold(
        (error) async {
          await _errorHandler.reportBusinessLogicError(
            error,
            StackTrace.current,
            feature: 'in_app_update',
            operation: 'start_immediate_update',
            businessContext: {
              'error_message': error.message,
              'update_info_available': state.updateInfo != null,
            },
          );
          state = state.copyWith(isLoading: false, error: error.message);
        },
        (_) {
          // Immediate update started successfully
          // The app will be blocked until update completes
        },
      );
    } catch (e, stackTrace) {
      await _errorHandler.reportBusinessLogicError(
        e,
        stackTrace,
        feature: 'in_app_update',
        operation: 'start_immediate_update',
        businessContext: {'error_message': e.toString()},
      );
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to start immediate update: $e',
      );
    }
  }

  /// Start a flexible update (downloads in background)
  Future<void> startFlexibleUpdate() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      if (state.updateInfo == null) {
        await _errorHandler.reportBusinessLogicError(
          Exception('Update info not available'),
          StackTrace.current,
          feature: 'in_app_update',
          operation: 'start_flexible_update',
          businessContext: {'reason': 'update_info_null'},
        );
        state = state.copyWith(
          isLoading: false,
          error: 'Update info not available',
        );
        return;
      }
      final result = await _updateService.startFlexibleUpdate(
        state.updateInfo!,
      );

      result.fold(
        (error) async {
          await _errorHandler.reportBusinessLogicError(
            error,
            StackTrace.current,
            feature: 'in_app_update',
            operation: 'start_flexible_update',
            businessContext: {
              'error_message': error.message,
              'update_info_available': state.updateInfo != null,
            },
          );
          state = state.copyWith(isLoading: false, error: error.message);
        },
        (_) {
          // Flexible update started successfully
          state = state.copyWith(isLoading: false);

          // Start monitoring the download progress
          _monitorFlexibleUpdate();
        },
      );
    } catch (e, stackTrace) {
      await _errorHandler.reportBusinessLogicError(
        e,
        stackTrace,
        feature: 'in_app_update',
        operation: 'start_flexible_update',
        businessContext: {'error_message': e.toString()},
      );
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to start flexible update: $e',
      );
    }
  }

  /// Complete a flexible update (install the downloaded update)
  Future<void> completeFlexibleUpdate() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _updateService.completeFlexibleUpdate();

      result.fold(
        (error) async {
          await _errorHandler.reportBusinessLogicError(
            error,
            StackTrace.current,
            feature: 'in_app_update',
            operation: 'complete_flexible_update',
            businessContext: {
              'error_message': error.message,
              'update_downloaded': state.isUpdateDownloaded,
            },
          );
          state = state.copyWith(isLoading: false, error: error.message);
        },
        (_) {
          // Update completed successfully
        },
      );
    } catch (e, stackTrace) {
      await _errorHandler.reportBusinessLogicError(
        e,
        stackTrace,
        feature: 'in_app_update',
        operation: 'complete_flexible_update',
        businessContext: {'error_message': e.toString()},
      );
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to complete flexible update: $e',
      );
    }
  }

  /// Monitor flexible update progress
  void _monitorFlexibleUpdate() {
    // Check every 3 seconds if the flexible update is downloaded
    Future.delayed(const Duration(seconds: 3), () async {
      if (state.updateInfo == null) return;

      try {
        // Re-check the update info to get the latest status
        final updateInfoResult = await _updateService.checkForUpdate();
        await updateInfoResult.fold(
          (error) async {
            await _errorHandler.reportBusinessLogicError(
              error,
              StackTrace.current,
              feature: 'in_app_update',
              operation: 'monitor_flexible_update_check',
              businessContext: {
                'error_message': error.message,
                'monitoring_active': true,
              },
            );
            // If we can't check status, continue monitoring
            if (state.isUpdateAvailable && !state.isUpdateDownloaded) {
              _monitorFlexibleUpdate();
            }
          },
          (updateInfo) async {
            if (updateInfo != null) {
              // Check if the update is downloaded
              final downloadResult = await _updateService
                  .isFlexibleUpdateDownloaded(updateInfo);
              downloadResult.fold(
                (error) async {
                  await _errorHandler.reportBusinessLogicError(
                    error,
                    StackTrace.current,
                    feature: 'in_app_update',
                    operation: 'monitor_flexible_update_download_check',
                    businessContext: {
                      'error_message': error.message,
                      'monitoring_active': true,
                    },
                  );
                  // Continue monitoring on error
                  if (state.isUpdateAvailable && !state.isUpdateDownloaded) {
                    _monitorFlexibleUpdate();
                  }
                },
                (isDownloaded) {
                  if (isDownloaded && !state.isUpdateDownloaded) {
                    // Update downloaded! Update state
                    state = state.copyWith(
                      isUpdateDownloaded: true,
                      updateInfo: updateInfo,
                    );

                    // Notify that update is ready for installation
                    _notifyUpdateReadyForInstallation();
                  } else if (!isDownloaded &&
                      state.isUpdateAvailable &&
                      !state.isUpdateDownloaded) {
                    // Continue monitoring if update is still downloading
                    _monitorFlexibleUpdate();
                  }
                },
              );
            }
          },
        );
      } catch (e, stackTrace) {
        await _errorHandler.reportBusinessLogicError(
          e,
          stackTrace,
          feature: 'in_app_update',
          operation: 'monitor_flexible_update',
          businessContext: {
            'error_message': e.toString(),
            'monitoring_active': true,
          },
        );
        // Continue monitoring on exception
        if (state.isUpdateAvailable && !state.isUpdateDownloaded) {
          _monitorFlexibleUpdate();
        }
      }
    });
  }

  /// Notify that update is ready for installation
  /// This is called when a flexible update has finished downloading
  void _notifyUpdateReadyForInstallation() {
    // This method can be extended to show notifications or trigger UI updates
    // For now, the UI will react to the state change automatically
    // TODO: Add proper logging framework or notification system
  }

  /// Clear any error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Get update type as a human-readable string
  String get updatePriorityText {
    return state.isHighPriority ? 'Critical' : 'Normal';
  }

  /// Check if the current update is critical
  bool get isCriticalUpdate => state.isHighPriority;

  /// Check if the current update is normal
  bool get isNormalUpdate => !state.isHighPriority;

  /// Check if immediate update should be forced (for critical updates)
  bool get shouldForceImmediateUpdate {
    return state.isUpdateAvailable &&
        isCriticalUpdate &&
        (state.updateInfo?.immediateUpdateAllowed ?? false);
  }
}
